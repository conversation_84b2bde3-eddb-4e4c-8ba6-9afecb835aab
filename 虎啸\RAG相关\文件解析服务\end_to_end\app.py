import os
from magic_pdf.data.data_reader_writer import FileBasedDataWriter, FileBasedDataReader
from magic_pdf.data.dataset import PymuDocDataset
from magic_pdf.model.doc_analyze_by_custom_model import doc_analyze
from magic_pdf.config.enums import SupportedPdfParseMethod

from markitdown import MarkItDown
import pandas as pd
import subprocess
import traceback
from loguru import logger


def convert_file_to_pdf(file_path, output_dir):
    """Convert docx and ppt to pdf"""
    try:
        result = subprocess.run(
            ['libreoffice', '--headless', '--convert-to', 'pdf', '--outdir', output_dir, file_path],
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE
            )
        if result.returncode == 0:
            return True
        else:
            return False
    except Exception as e:
        traceback.print_exc()
        return False


def convert_xls_to_xslx(xls_file, xlsx_file):
    # 使用 pandas 读取 .xls 文件
    df = pd.read_excel(xls_file, sheet_name=None)  # sheet_name=None 读取所有的sheet

    # 将数据写入到 .xlsx 文件
    with pd.ExcelWriter(xlsx_file, engine='openpyxl') as writer:
        # 将每个sheet写入到 .xlsx 文件
        for sheet_name, data in df.items():
            data.to_excel(writer, sheet_name=sheet_name, index=False)
    return xlsx_file
    

def xlsx_processing(xlsx_file_path):
    """analysis xlsx by markitdown"""
    
    md = MarkItDown()
    result = md.convert(xlsx_file_path)
    
    return result.text_content


def pdf_processing(pdf_file_path):
    """process pdf file analysis"""
    
    # generate pdf_file_name and name_without_suff
    pdf_file_name = pdf_file_path.split("/")[-1]
    name_without_suff = pdf_file_name.split(".")[0]
    suff = pdf_file_name.split(".")[-1]

    # prepare env
    local_image_dir, local_md_dir = f"output/{name_without_suff}" + "_" + suff + "/images", f"output/{name_without_suff}" + "_" + suff
    image_dir = str(os.path.basename(local_image_dir))

    os.makedirs(local_image_dir, exist_ok=True)

    image_writer, md_writer = FileBasedDataWriter(local_image_dir), FileBasedDataWriter(local_md_dir)

    # read bytes
    reader1 = FileBasedDataReader("")
    pdf_bytes = reader1.read(pdf_file_path)  # read the pdf content

    # proc
    ## Create Dataset Instance
    ds = PymuDocDataset(pdf_bytes)

    ## inference
    if ds.classify() == SupportedPdfParseMethod.OCR:
        infer_result = ds.apply(doc_analyze, ocr=True)

        ## pipeline
        pipe_result = infer_result.pipe_ocr_mode(image_writer)

    else:
        infer_result = ds.apply(doc_analyze, ocr=False)

        ## pipeline
        pipe_result = infer_result.pipe_txt_mode(image_writer)

    ### draw model result on each page
    infer_result.draw_model(os.path.join(local_md_dir, f"{name_without_suff}_model.pdf"))

    ### get model inference result
    model_inference_result = infer_result.get_infer_res()

    ### draw layout result on each page
    pipe_result.draw_layout(os.path.join(local_md_dir, f"{name_without_suff}_layout.pdf"))

    ### draw spans result on each page
    pipe_result.draw_span(os.path.join(local_md_dir, f"{name_without_suff}_spans.pdf"))

    ### get markdown content
    md_content = pipe_result.get_markdown(image_dir)

    ### dump markdown
    pipe_result.dump_md(md_writer, f"{name_without_suff}.md", image_dir)

    ### get content list content
    content_list_content = pipe_result.get_content_list(image_dir)

    ### dump content list
    pipe_result.dump_content_list(md_writer, f"{name_without_suff}_content_list.json", image_dir)

    ### get middle json
    middle_json_content = pipe_result.get_middle_json()

    ### dump middle json
    pipe_result.dump_middle_json(md_writer, f'{name_without_suff}_middle.json')
    
    ### print analysis end
    logger.info(f'file analysis end: {pdf_file_name}')


def main():
    file_path = "./data/个人借款合同.pdf" 
    # file_path = "./data/f539527172fa4f55b8ed61f3a856b2f7.pdf"
    file_name = file_path.split("/")[-1]
    name_without_suff = file_name.split(".")[0]
    file_type = file_name.split(".")[-1]
    result_folder_path = f"output/{name_without_suff}" + "_" + file_type
    os.makedirs(result_folder_path, exist_ok=True)
    
    # process pdf/image
    if file_type in ['pdf', 'PDF', 'jpg', 'png']:
        pdf_processing(file_path)
        
    # process word/ppt
    elif file_type in ['doc', 'ppt', 'docx', 'pptx']:
        # convert to pdf
        convert_pdf_result = convert_file_to_pdf(file_path, result_folder_path)
        if convert_pdf_result:
            temp_pdf_file_path = result_folder_path + "/" + name_without_suff + ".pdf"
            pdf_processing(temp_pdf_file_path)
        else:
            logger.error('convert file to pdf failed!')
    
    # process excel
    elif file_type in ['xls', 'xlsx']:
        if file_type == 'xls':
            # convert to xlsx
            temp_xlsx_file_path = result_folder_path + "/" + name_without_suff + ".xlsx"
            convert_xlsx_result = convert_xls_to_xslx(file_path, temp_xlsx_file_path)
            if convert_xlsx_result:
                xlsx_processing(temp_xlsx_file_path)
            else:
                logger.error('convert file to xlsx failed!')
            
    else:
        logger.warning("Unsupported file type: {}".format(file_type))
            
        
if __name__ == '__main__':
    main()