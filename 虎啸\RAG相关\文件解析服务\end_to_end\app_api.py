import os
from loguru import logger

from magic_pdf.data.data_reader_writer import FileBasedDataWriter, FileBasedDataReader
from magic_pdf.data.dataset import PymuDocDataset
from magic_pdf.model.doc_analyze_by_custom_model import doc_analyze
from magic_pdf.config.enums import SupportedPdfParseMethod

from markitdown import MarkItDown
from PIL import Image
import pandas as pd
import subprocess
import traceback
from werkzeug.utils import secure_filename

import tarfile
from datetime import timedelta
from minio import Minio
from minio.error import S3Error
from config import *
from flask import Flask, request, jsonify

app = Flask(__name__)

# 设置上传文件的保存路径
UPLOAD_FOLDER = 'data'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# 允许的文件扩展名
ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png', 'PDF'}


def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


def convert_file_to_pdf(file_path, output_dir):
    """Convert docx and ppt to pdf"""
    try:
        result = subprocess.run(
            ['libreoffice', '--headless', '--convert-to', 'pdf', '--outdir', output_dir, file_path],
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE
            )
        if result.returncode == 0:
            return True
        else:
            return False
    except Exception as e:
        traceback.print_exc()
        return False


def convert_xls_to_xslx(xls_file, xlsx_file):
    try:
        # 使用 pandas 读取 .xls 文件
        df = pd.read_excel(xls_file, sheet_name=None)  # sheet_name=None 读取所有的sheet

        # 将数据写入到 .xlsx 文件
        with pd.ExcelWriter(xlsx_file, engine='openpyxl') as writer:
            # 将每个sheet写入到 .xlsx 文件
            for sheet_name, data in df.items():
                data.to_excel(writer, sheet_name=sheet_name, index=False)
        return True
    except Exception as e:
        traceback.print_exc()
        return False
    
    
def convert_image_to_pdf(image_path, pdf_path):
         try:
             # 打开图片
             with Image.open(image_path) as img:
                 # 检查图片是否有效
                 if img is None:
                     raise ValueError("无法打开图片")

                 # 如果需要，将图片转换为PDF
                 img.convert('RGB').save(pdf_path)
                 return True

         except Exception as e:
             logger.error(f"处理图片时出错: {e}")
             return False
         
         
def xlsx_processing(xlsx_file_path, name_without_suff, file_type, md_path):
    """analysis xlsx by markitdown"""
    try:
        md = MarkItDown()
        result = md.convert(xlsx_file_path)
        md_text = result.text_content
        with open(md_path, "a") as file:
            file.write(md_text)
        output_dir = "output"
        result_dir_name = name_without_suff + "_" + file_type
        ### 上传成功返回可下载的url，失败返回None
        upload_result = upload_file_parse(output_dir, result_dir_name)
        if upload_result:
            return upload_result
        else:
            return None
            return md_path
    except Exception as e:
        logger.err("Failed to pearse to excel:", e)
        return None


def pdf_processing(pdf_file_path, file_type):
    """process pdf file analysis"""
    try:
        # generate pdf_file_name and name_without_suff
        pdf_file_name = pdf_file_path.split("/")[-1]
        name_without_suff = pdf_file_name.split(".")[0]
        # suff = pdf_file_name.split(".")[-1]

        # prepare env
        local_image_dir, local_md_dir = f"output/{name_without_suff}" + "_" + file_type + "/images", f"output/{name_without_suff}" + "_" + file_type
        image_dir = str(os.path.basename(local_image_dir))

        os.makedirs(local_image_dir, exist_ok=True)

        image_writer, md_writer = FileBasedDataWriter(local_image_dir), FileBasedDataWriter(local_md_dir)

        # read bytes
        reader1 = FileBasedDataReader("")
        pdf_bytes = reader1.read(pdf_file_path)  # read the pdf content

        # proc
        ## Create Dataset Instance
        ds = PymuDocDataset(pdf_bytes)

        ## inference
        if ds.classify() == SupportedPdfParseMethod.OCR:
            infer_result = ds.apply(
                doc_analyze, 
                ocr=True
            )
            pipe_result = infer_result.pipe_ocr_mode(image_writer)
        else:
            infer_result = ds.apply(
                doc_analyze, 
                ocr=False
            )
            pipe_result = infer_result.pipe_txt_mode(image_writer)

        ### draw model result on each page
        infer_result.draw_model(os.path.join(local_md_dir, f"{name_without_suff}_model.pdf"))

        ### get model inference result
        model_inference_result = infer_result.get_infer_res()

        ### draw layout result on each page
        pipe_result.draw_layout(os.path.join(local_md_dir, f"{name_without_suff}_layout.pdf"))

        ### draw spans result on each page
        pipe_result.draw_span(os.path.join(local_md_dir, f"{name_without_suff}_spans.pdf"))

        ### get markdown content
        md_content = pipe_result.get_markdown(image_dir)

        ### dump markdown
        pipe_result.dump_md(md_writer, f"{name_without_suff}.md", image_dir)

        ### get content list content
        content_list_content = pipe_result.get_content_list(image_dir)

        ### dump content list
        pipe_result.dump_content_list(md_writer, f"{name_without_suff}_content_list.json", image_dir)

        ### get middle json
        middle_json_content = pipe_result.get_middle_json()

        ### dump middle json
        pipe_result.dump_middle_json(md_writer, f'{name_without_suff}_middle.json')
        
        ### print analysis end
        logger.info(f'file analysis end: {pdf_file_name}')
        
        ### upload result to minio
        output_dir = "output"
        result_dir_name = name_without_suff + "_" + file_type
        ### 上传成功返回可下载的url，失败返回None
        upload_result = upload_file_parse(output_dir, result_dir_name)
        if upload_result:
            return upload_result
        else:
            return None

    except Exception as e:
        logger.error(f"PDF处理失败: {str(e)}")
        return None


def create_tar(output_dir, result_dir_name):
    """将结果目录打包成tar文件"""
    tar_path = os.path.join(output_dir, f"{result_dir_name}.tar.gz")
    with tarfile.open(tar_path, "w:gz") as tar:
        tar.add(os.path.join(output_dir, result_dir_name), arcname=result_dir_name)
    return tar_path


def upload_to_minio(tar_path, bucket_name, object_name, minio_client):
    """将tar文件上传到MinIO"""
    try:
        # 检查bucket是否存在，不存在则创建
        if not minio_client.bucket_exists(bucket_name):
            minio_client.make_bucket(bucket_name)

        # 上传文件
        minio_client.fput_object(bucket_name, object_name, tar_path)
        logger.info(f"Successfully uploaded {object_name} to bucket {bucket_name}.")
        # 生成预签名的下载URL，有效期为7天（604800秒）
        download_url = minio_client.presigned_get_object(bucket_name, object_name, expires=timedelta(seconds=604800))

        return download_url
    except S3Error as e:
        logger.error(f"Error occurred: {e}")
        return None


def upload_file_parse(output_dir, result_dir_name):
    """将解析结果打成tar包并上传至minio"""
    try:
        # MinIO客户端配置
        minio_client = Minio(
            minio_endpoint,  # MinIO服务器地址
            access_key=minio_access_key,  
            secret_key=minio_secret_key, 
            secure=False  # 如果使用HTTP则设置为False
        )
        # bucket_name = bucket_name  
        object_name = object_name_prefix + f"{result_dir_name}.tar.gz"

        # 创建tar包
        tar_path = create_tar(output_dir, result_dir_name)

        # 上传到MinIO
        download_url = upload_to_minio(tar_path, bucket_name, object_name, minio_client)
        if download_url:
            return download_url
        else:
            logger.error('Failed to generate download URL.')
            return None
    except Exception as e:
        print('Failed to upload file parse result: ', e)
        return None


@app.route('/api/v1/file/parser', methods=['POST'])
def file_parser():
    # 检查请求中是否包含文件
    if 'file' not in request.files:
        return jsonify({'error': 'No file part'}), 400

    file = request.files['file']
    # 如果用户没有选择文件
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400

    if file and allowed_file(file.filename):
        # filename = secure_filename(file.filename)
        filename = file.filename
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)

        name_without_suff = filename.rsplit('.', 1)[0]
        file_type = filename.rsplit('.', 1)[1].lower()
        result_folder_path = f"output/{name_without_suff}" + "_" + file_type
        os.makedirs(result_folder_path, exist_ok=True)
        
        ### 根据文件类型调用相应的处理函数
        # 处理pdf
        if file_type in ['pdf']:
            result = pdf_processing(file_path, file_type)
        
        # 处理图片
        elif file_type in ['jpg', 'png', 'jpeg']:
            temp_pdf_file_path = os.path.join(result_folder_path, name_without_suff + '.pdf')
            convert_result = convert_image_to_pdf(file_path, temp_pdf_file_path)
            if convert_result:
                result = pdf_processing(temp_pdf_file_path, file_type)
        
        # 处理word/ppt
        elif file_type in ['doc', 'ppt', 'docx', 'pptx']:
            convert_result = convert_file_to_pdf(file_path, result_folder_path)
            if convert_result:
                temp_pdf_file_path = os.path.join(result_folder_path, name_without_suff + '.pdf')
                result = pdf_processing(temp_pdf_file_path, file_type)
            else:
                return jsonify({'error': 'Failed to convert file to PDF'}), 500

        # 处理excel
        elif file_type in ['xls', 'xlsx']:
            if file_type == 'xls':
                temp_xlsx_file_path = os.path.join(result_folder_path, name_without_suff + '.xlsx')
                convert_result = convert_xls_to_xslx(file_path, temp_xlsx_file_path)
                if convert_result:
                    md_path = os.path.join(result_folder_path, name_without_suff + '.md')
                    result = xlsx_processing(temp_xlsx_file_path, name_without_suff, file_type, md_path)
                else:
                    return jsonify({'error': 'Failed to convert file to XLSX'}), 500
            else:
                md_path = os.path.join(result_folder_path, name_without_suff + '.md')
                result = xlsx_processing(file_path, name_without_suff, file_type, md_path)
        
        else:
            return jsonify({'error': 'Unsupported file type'}), 400

        if result:
            return jsonify({'result': result}), 200
        else:
            return jsonify({'error': 'Parser failed'}), 500

    return jsonify({'error': 'File type not allowed'}), 400


if __name__ == '__main__':
    app.run(host="0.0.0.0", port=8006, debug=False)

    