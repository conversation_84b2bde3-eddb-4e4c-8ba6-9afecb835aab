import os
from docx import Document
from collections import defaultdict
from pdf2docx import Converter

from loguru import logger
from flask import Flask, request, jsonify
from werkzeug.utils import secure_filename

import tarfile
from datetime import timedelta
from minio import Minio
from minio.error import S3Error
from config import *

app = Flask(__name__)

# 允许的文件扩展名
ALLOWED_EXTENSIONS = {'pdf', 'PDF'}
# 设置上传文件的保存路径
UPLOAD_FOLDER = 'data'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER


def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def pdf_to_docx(pdf_path, docx_path):
    # convert pdf to docx
    cv = Converter(pdf_path)
    cv.convert(docx_path,)  # all pages by default
    # settings = {
    #         'min_section_height': 10.0,
    #         'connected_border_tolerance': 1.0,
    #         'max_border_width': 8.0,
    #         'min_border_clearance': 1.0,
    #         'max_line_spacing_ratio': 2.0,
    #         'line_overlap_threshold': 0.7,
    #         'line_break_width_ratio': 0.2,
    #         'line_break_free_space_ratio': 0.05,
    #         'line_separate_threshold': 2.0,
    #         'new_paragraph_free_space_ratio': 0.95,
    #         'lines_left_aligned_threshold': 2.0,
    #         'lines_right_aligned_threshold': 2.0,
    #         'lines_center_aligned_threshold': 4.0,
    #         'extract_stream_table': True,
    #         'parse_lattice_table': True,
    #         'parse_stream_table': True,
    #         'delete_end_line_hyphen': True,
    #         # 其他参数保持默认即可
    #     }
    # cv.convert(docx_path, settings=settings)
    cv.close()
    return docx_path

def build_merge_map(table):
    rows = len(table.rows)
    cols = len(table.columns)

    tc_map = defaultdict(list)  # _tc → 所有(row, col)坐标
    cell_map = [[None for _ in range(cols)] for _ in range(rows)]

    for i in range(rows):
        for j in range(cols):
            cell = table.cell(i, j)
            tc = cell._tc
            cell_map[i][j] = cell
            tc_map[tc].append((i, j))

    merge_info = {}
    skip_cells = set()

    for tc, coords in tc_map.items():
        rows_ = [r for r, _ in coords]
        cols_ = [c for _, c in coords]
        min_row, max_row = min(rows_), max(rows_)
        min_col, max_col = min(cols_), max(cols_)
        rowspan = max_row - min_row + 1
        colspan = max_col - min_col + 1
        merge_info[(min_row, min_col)] = {
            'rowspan': rowspan,
            'colspan': colspan,
            'cell': cell_map[min_row][min_col]
        }

        for r in range(min_row, min_row + rowspan):
            for c in range(min_col, min_col + colspan):
                if (r, c) != (min_row, min_col):
                    skip_cells.add((r, c))

    return merge_info, skip_cells

def table_to_html_robust(table):
    merge_info, skip_cells = build_merge_map(table)
    rows = len(table.rows)
    cols = len(table.columns)

    html = '<table border="1" cellspacing="0" cellpadding="5">\n'
    for i in range(rows):
        html += "  <tr>\n"
        for j in range(cols):
            if (i, j) in skip_cells:
                continue
            cell_info = merge_info.get((i, j))
            if cell_info:
                rowspan = cell_info['rowspan']
                colspan = cell_info['colspan']
                cell = cell_info['cell']
                rowspan_attr = f' rowspan="{rowspan}"' if rowspan > 1 else ""
                colspan_attr = f' colspan="{colspan}"' if colspan > 1 else ""
                content = cell.text.strip().replace('\n', '<br>')
                html += f'    <td{rowspan_attr}{colspan_attr}>{content}</td>\n'
            else:
                html += '    <td></td>\n'
        html += "  </tr>\n"
    html += "</table>\n"
    return html

def extract_tables_from_word(docx_path):
    doc = Document(docx_path)
    html_tables = []
    for i, table in enumerate(doc.tables):
        # 跳过只有一行的表格
        if len(table.rows) <= 1:
            continue
        html = table_to_html_robust(table)
        html_tables.append(html)
    return html_tables

def save_html_file(html_tables, output_path):
    with open(output_path, "w", encoding="utf-8") as f:
        f.write("<html><head><meta charset='utf-8'></head><body>\n")
        for i, table_html in enumerate(html_tables):
            f.write(f"<h3>Table {i+1}</h3>\n")
            f.write(table_html + "<br><br>\n")
        f.write("</body></html>")
        
def save_md_file(md_text, output_path, name_without_suff, file_type):
    with open(output_path, "w", encoding="utf-8") as f:
        f.write(md_text)
    
    ### upload result to minio
    output_dir = "output"
    result_dir_name = name_without_suff + "_" + file_type
    ### 上传成功返回可下载的url，失败返回None
    upload_result = upload_file_parse(output_dir, result_dir_name)
    if upload_result:
        return upload_result
    else:
        return None
        
def docx_to_markdown(docx_file):
    doc = Document(docx_file)
    markdown_text = ""

    paragraph_list = doc.paragraphs
    table_list = doc.tables
    image_list = doc.inline_shapes

    order_list = []
    for element in doc.element.body:
        if element.tag.endswith('tbl'):
            order_list.append("table")
        elif element.tag.endswith('p'):
            order_list.append("paragraph")
        elif element.tag.endswith('drawing') or element.tag.endswith('pict'):
            order_list.append("image")

    paragraph_index = 0
    table_index = 0
    image_index = 0
    for order in order_list:
        if order == "paragraph":
            # 判断是否是标题
            paragraph_formate = paragraph_list[paragraph_index].style.name
            if paragraph_formate.startswith("Heading"):
                formate_num = eval(paragraph_formate.split(" ")[1])
                # print(formate_num)
                markdown_text += "#" * (formate_num+1) + " "

            # 提取文本内容
            markdown_text += paragraph_list[paragraph_index].text + "\n\n"
            paragraph_index += 1
        elif order == "table":
            # if len(table_list[table_index].rows) <= 1:
            #     continue
            markdown_text += table_to_html_robust(table_list[table_index]) + "\n"
            table_index += 1
        elif order == "image":
            markdown_text += "![](" + image_list[image_index].src + ")\n"
            image_index += 1

    return markdown_text

def create_tar(output_dir, result_dir_name):
    """将结果目录打包成tar文件"""
    tar_path = os.path.join(output_dir, f"{result_dir_name}.tar.gz")
    with tarfile.open(tar_path, "w:gz") as tar:
        tar.add(os.path.join(output_dir, result_dir_name), arcname=result_dir_name)
    return tar_path


def upload_to_minio(tar_path, bucket_name, object_name, minio_client):
    """将tar文件上传到MinIO"""
    try:
        # 检查bucket是否存在，不存在则创建
        if not minio_client.bucket_exists(bucket_name):
            minio_client.make_bucket(bucket_name)

        # 上传文件
        minio_client.fput_object(bucket_name, object_name, tar_path)
        logger.info(f"Successfully uploaded {object_name} to bucket {bucket_name}.")
        # 生成预签名的下载URL，有效期为7天（604800秒）
        download_url = minio_client.presigned_get_object(bucket_name, object_name, expires=timedelta(seconds=604800))

        return download_url
    except S3Error as e:
        logger.error(f"Error occurred: {e}")
        return None
    
def upload_file_parse(output_dir, result_dir_name):
    """将解析结果打成tar包并上传至minio"""
    try:
        # MinIO客户端配置
        minio_client = Minio(
            minio_endpoint,  # MinIO服务器地址
            access_key=minio_access_key,  
            secret_key=minio_secret_key, 
            secure=False  # 如果使用HTTP则设置为False
        )
        # bucket_name = bucket_name  
        object_name = object_name_prefix + f"{result_dir_name}.tar.gz"

        # 创建tar包
        tar_path = create_tar(output_dir, result_dir_name)

        # 上传到MinIO
        download_url = upload_to_minio(tar_path, bucket_name, object_name, minio_client)
        if download_url:
            return download_url
        else:
            logger.error('Failed to generate download URL.')
            return None
    except Exception as e:
        print('Failed to upload file parse result: ', e)
        return None
    
def test():
    pdf_path = "/Users/<USER>/Downloads/贵州茅台2024年三季报.pdf"
    docx_path = "/Users/<USER>/Documents/wiseweb/ToolCodes/文档处理/贵州茅台2024年三季报.docx"
    html_path = "贵州茅台2024年三季报.html"
    # pdf_path = "/Users/<USER>/Downloads/TYZL-RM02002-2024A天翼融资租赁有限公司权限列表（2024年）.pdf"
    # docx_path = "/Users/<USER>/Documents/wiseweb/ToolCodes/文档处理/TYZL-RM02002-2024A天翼融资租赁有限公司权限列表（2024年）.docx"
    # html_path = "TYZL-RM02002-2024A天翼融资租赁有限公司权限列表（2024年）.html"
    # PDF转docx
    pdf_to_docx(pdf_path, docx_path)
    # docx转html
    html_tables = extract_tables_from_word(docx_path)
    save_html_file(html_tables, html_path)
    print(f"✅ 支持合并单元格的表格 HTML 提取完成！输出文件：{html_path}")
    
    # docx_path = "/Users/<USER>/Documents/wiseweb/ToolCodes/文档处理/财报型pdf转word处理样例.docx"
    # md_file = "财报型pdf转word处理样例.md"
    # markdown_text = docx_to_markdown(docx_path)
    # save_md_file(markdown_text, md_file)
    # print(f"✅ 输出markdown文件：{md_file}")

@app.route('/api/v1/financial_report/parser', methods=['POST'])
def pdf_to_md():
    # 检查请求中是否包含文件
    if 'file' not in request.files:
        return jsonify({'error': 'No file part'}), 400

    file = request.files['file']
    # 如果用户没有选择文件
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400
    
    
    if file and allowed_file(file.filename):
        filename = file.filename
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)
        
        name_without_suff = filename.rsplit('.', 1)[0]
        file_type = filename.rsplit('.', 1)[1].lower()
        result_folder_path = f"output/{name_without_suff}" + "_" + file_type
        os.makedirs(result_folder_path, exist_ok=True)
        docx_path = result_folder_path + "/" + name_without_suff + ".docx"
        md_file = result_folder_path + "/" + name_without_suff + ".md"
        
        docx_path = pdf_to_docx(file_path, docx_path)
        markdown_text = docx_to_markdown(docx_path)
        result = save_md_file(markdown_text, md_file, name_without_suff, file_type)
        logger.info(f"✅ 输出markdown文件：{md_file}")
        
        if result:
            return jsonify({'result': result}), 200
        else:
            return jsonify({'error': 'Parser failed'}), 500
        
    return jsonify({'error': 'File type not allowed'}), 400


if __name__ == "__main__":
    # docx_path = "/Users/<USER>/Documents/wiseweb/ToolCodes/文档处理/财报型pdf转word处理样例.docx"  # 替换为你的 Word 文件路径
    # html_path = "财报型pdf转word处理样例_v2_3.html"
    # html_tables = extract_tables_from_word(docx_path)
    # save_html_file(html_tables, html_path)
    # print("✅ 支持合并单元格的表格 HTML 提取完成！输出文件：tables_output.html")
    # test()
    app.run(host="0.0.0.0", port=8005, debug=False)