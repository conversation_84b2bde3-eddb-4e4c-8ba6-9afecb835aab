from paddleocr import PaddleOCR
import base64
import os
import numpy as np
import cv2


ocr_engine = PaddleOCR(use_angle_cls=True, lang="ch", use_gpu=False, show_log=False)



def image_to_base64(image_path: str) -> str:
    """Function to convert an image to base64"""
    
    with open(image_path, "rb") as image_file:
        # Read the image and encode it in base64
        encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
    return encoded_string



def base64_to_image(base64_str: str, save_path: str = './source_images'):
    """将base64字符串转换为OpenCV图像"""
    try:
        img_data = base64.b64decode(base64_str)
        nparr = np.frombuffer(img_data, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        # 确保保存目录存在
        if not os.path.exists(save_path):
            os.makedirs(save_path)
        
        # 生成保存文件名
        file_path = os.path.join(save_path, 'source_image.png')
        
        # 保存图像
        cv2.imwrite(file_path, img)
        
        return img
    except Exception as e:
        print(e)



def main():
    image_path = "./Snipaste_2025-03-26_16-37-48.jpg"
    encoded_string = image_to_base64(image_path)
    img = base64_to_image(encoded_string)
    res = ocr_engine.ocr(img)
    context = ""
    for result in res[0]:
        text = result[1][0] + "\n"
        context += text
    print(context)
    
    
    
if __name__ == '__main__':
    main()